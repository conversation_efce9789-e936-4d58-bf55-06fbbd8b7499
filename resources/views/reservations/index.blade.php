@extends('layouts.admin')

@section('title', 'Reservations - SMP Online')

@section('content')
    <!-- <PERSON> Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">Reservations Management</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Reservations</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- Page Header Close -->

    <!-- Success/Error Messages -->
    @if (session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="ti ti-check-circle me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if (session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="ti ti-exclamation-triangle me-2"></i>{{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <!-- Quick Stats -->
    <div class="row mb-4">
        <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12">
            <div class="card custom-card">
                <div class="card-body">
                    <div class="d-flex align-items-top">
                        <div class="me-3">
                            <span class="avatar avatar-md bg-primary-transparent">
                                <i class="ti ti-calendar-event fs-16"></i>
                            </span>
                        </div>
                        <div class="flex-fill">
                            <div class="d-flex align-items-center justify-content-between mb-1">
                                <h6 class="mb-0">Upcoming Reservations</h6>
                            </div>
                            <div class="d-flex align-items-center justify-content-between">
                                <div>
                                    <span class="h4 fw-semibold mb-0">{{ $upcomingCount }}</span>
                                </div>
                                <div class="text-success">
                                    <i class="ti ti-trending-up me-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12">
            <div class="card custom-card">
                <div class="card-body">
                    <div class="d-flex align-items-top">
                        <div class="me-3">
                            <span class="avatar avatar-md bg-success-transparent">
                                <i class="ti ti-check fs-16"></i>
                            </span>
                        </div>
                        <div class="flex-fill">
                            <div class="d-flex align-items-center justify-content-between mb-1">
                                <h6 class="mb-0">Total Reservations</h6>
                            </div>
                            <div class="d-flex align-items-center justify-content-between">
                                <div>
                                    <span class="h4 fw-semibold mb-0">{{ $totalReservationsCount }}</span>
                                </div>
                                <div class="text-success">
                                    <i class="ti ti-trending-up me-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Reservations List -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">
                        <i class="ti ti-list me-2"></i>Reservations
                    </div>
                    <div class="d-flex gap-2">
                        <a href="{{ route('reservations.create') }}" class="btn btn-primary btn-sm">
                            <i class="ti ti-plus me-1"></i>New Reservation
                        </a>
                        <a href="{{ route('calendar.index') }}" class="btn btn-success btn-sm">
                            <i class="ti ti-calendar me-1"></i>View Calendar
                        </a>
                    </div>
                </div>

                <!---------------------------------------------------------------------------------------------------------------------->
                <!-- Search and Filter Form -->
                <form method="GET" action="{{ route('reservations.index') }}" class="px-4">
                    <div class="row bg-light rounded mt-3 mb-0 py-2">
                        <div class="col-xl-2">
                            <select name="status" class="form-select">
                                <option value="">All Statuses</option>
                                @foreach (\App\Models\Booking::getStatuses() as $key => $status)
                                    <option value="{{ $key }}" {{ request('status') === $key ? 'selected' : '' }}>
                                        {{ $status }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-xl-3">
                            <select name="field_id" class="form-select">
                                <option value="">All Fields</option>
                                @foreach ($fields as $field)
                                    <option value="{{ $field->id }}"
                                        {{ request('field_id') == $field->id ? 'selected' : '' }}>{{ $field->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-xl-2">
                            <input type="date" name="date_from" value="{{ request('date_from') }}"
                                placeholder="From Date" class="form-control">
                        </div>
                        <div class="col-xl-2">
                            <input type="date" name="date_to" value="{{ request('date_to') }}" placeholder="To Date"
                                class="form-control">
                        </div>
                        <div class="col-xl-3 mt-1">
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-info btn-sm flex-fill">
                                    <i class="ti ti-search me-1"></i>Filter
                                </button>
                                <a href="{{ route('reservations.index') }}" class="btn btn-info btn-sm flex-fill">
                                    <i class="ti ti-refresh me-1"></i>Clear
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
                <!---------------------------------------------------------------------------------------------------------------------->


                <div class="card-body">
                    @if ($reservations->count() > 0)
                        <div class="table-responsive">
                            <table class="table text-nowrap table-hover">
                                <thead>
                                    <tr>
                                        <th>Field</th>
                                        <th>Date & Time</th>
                                        <th>Customer</th>
                                        <th>Cost</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($reservations as $reservation)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="me-2">
                                                        <span class="avatar avatar-sm bg-light">
                                                            <i class="ti ti-building-stadium fs-12"></i>
                                                        </span>
                                                    </div>
                                                    <div>
                                                        <span class="fw-semibold">{{ $reservation->field->name }}</span>
                                                        <br>
                                                        <small class="text-muted">{{ $reservation->field->type }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <span
                                                        class="fw-semibold">{{ $reservation->booking_date->format('M d, Y') }}</span>
                                                    <br>
                                                    <small class="text-muted">{{ $reservation->time_range }}</small>
                                                    <br>
                                                    <span class="badge bg-primary-transparent text-primary">Duration:
                                                        {{ $reservation->duration_hours }}
                                                        {{ Str::plural('hour', $reservation->duration_hours) }}</span>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="fw-semibold">{{ $reservation->customer_display_name }}</span>
                                            </td>
                                            <td>
                                                <span class="fw-semibold text-success">XCG
                                                    {{ number_format($reservation->total_cost, 2) }}</span>
                                            </td>
                                            <td>
                                                <span
                                                    class="badge bg-{{ $reservation->status_color }}-transparent text-{{ $reservation->status_color }}">
                                                    {{ $reservation->status }}
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    @if (auth()->user()->isAdmin() || $reservation->user_id === auth()->id())
                                                        <a href="{{ route('reservations.show', $reservation) }}"
                                                            class="btn btn-sm btn-info" title="View Details">
                                                            <i class="ti ti-eye"></i>
                                                        </a>
                                                        @if ($reservation->canBeModified())
                                                            <a href="{{ route('reservations.edit', $reservation) }}"
                                                                class="btn btn-sm btn-warning" title="Edit">
                                                                <i class="ti ti-edit"></i>
                                                            </a>
                                                        @endif
                                                        <!--@if ($reservation->canBeCancelled())
    <form method="POST"
                                                                                                                                                                                                    action="{{ route('reservations.cancel', $reservation) }}"
                                                                                                                                                                                                    class="d-inline"
                                                                                                                                                                                                    onsubmit="return confirm('Are you sure you want to cancel this reservation?')">
                                                                                                                                                                                                    @csrf
                                                                                                                                                                                                    <button type="submit" class="btn btn-sm btn-danger"
                                                                                                                                                                                                        title="Cancel">
                                                                                                                                                                                                        <i class="ti ti-x"></i>
                                                                                                                                                                                                    </button>
                                                                                                                                                                                                </form>
    @endif-->
                                                        <!------------------------------------------------------------->
                                                        @if ($reservation->isCancelled() && $reservation->canBeUncancelled())
                                                            <form method="POST"
                                                                action="{{ route('reservations.uncancel', $reservation) }}"
                                                                class="d-inline"
                                                                onsubmit="return confirm('Are you sure you want to restore this reservation?')">
                                                                @csrf
                                                                <button type="submit" class="btn btn-sm btn-outline-dark"
                                                                    title="Restore">
                                                                    <i class="ti ti-arrow-left"></i>
                                                                </button>
                                                            </form>
                                                        @elseif ($reservation->canBeCancelled())
                                                            <button type="button"
                                                                class="btn btn-sm btn-danger cancel-reservation-form"
                                                                title="Cancel"
                                                                data-cancel-url="{{ route('reservations.cancel', $reservation) }}">
                                                                <i class="ti ti-x"></i>
                                                            </button>
                                                        @endif
                                                        <!------------------------------------------------------------->
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="booking-pagination">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="pagination-info">
                                    Showing {{ $reservations->firstItem() }} to {{ $reservations->lastItem() }}
                                    of {{ $reservations->total() }} reservations
                                </div>
                                <div class="admin-pagination">
                                    {{ $reservations->links() }}
                                </div>
                            </div>
                        </div>
                    @else
                        <!--<div class="text-center py-5">
                                                                                                                                                                            <div class="mb-3">
                                                                                                                                                                                <i class="ti ti-calendar-x fs-48 text-muted"></i>
                                                                                                                                                                            </div>
                                                                                                                                                                            <h5 class="text-muted">No Reservations Found</h5>
                                                                                                                                                                            <p class="text-muted">You haven't made any reservations yet.</p>
                                                                                                                                                                            <a href="{{ route('reservations.create') }}" class="btn btn-primary">
                                                                                                                                                                                <i class="ti ti-plus me-1"></i>Make Your First Reservation
                                                                                                                                                                            </a>
                                                                                                                                                                        </div> -->
                        <div class="text-center py-5">
                            <div class="mb-3">
                                <i class="ti ti-calendar-x fs-48 text-muted"></i>
                            </div>

                            @if (request()->hasAny(['status', 'field_id', 'date_from', 'date_to']) &&
                                    (request('status') || request('field_id') || request('date_from') || request('date_to')))
                                <h5 class="text-muted">No Reservations Match Your Filters
                                </h5>
                                <p class="text-muted">Try adjusting the filter options.</p>
                                <a href="{{ route('reservations.index') }}" class="btn btn-secondary">
                                    <i class="ti ti-refresh me-1"></i>Clear Filters
                                </a>
                            @else
                                <h5 class="text-muted">No Reservations Found</h5>
                                <p class="text-muted">You haven't made any reservations yet.</p>
                                <a href="{{ route('reservations.create') }}" class="btn btn-primary">
                                    <i class="ti ti-plus me-1"></i>Make Your First Reservation
                                </a>
                            @endif
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
/**
 * Dynamic Form Creation for Cancel Reservation
 *
 * This script replaces the static HTML forms with dynamically created forms
 * that are submitted via JavaScript while maintaining the same functionality
 * including CSRF protection and confirmation dialogs.
 */
document.addEventListener('DOMContentLoaded', function() {
    // Attach click event listeners to all cancel reservation buttons
    document.querySelectorAll('.cancel-reservation-form').forEach(button => {
        button.addEventListener('click', function() {
            const cancelUrl = this.dataset.cancelUrl;

            // Show confirmation dialog (same as original implementation)
            if (confirm('Are you sure you want to cancel this reservation?')) {
                submitDynamicCancelForm(cancelUrl);
            }
        });
    });
});

/**
 * Creates and submits a form dynamically for reservation cancellation
 * @param {string} actionUrl - The URL to submit the cancellation request to
 */
function submitDynamicCancelForm(actionUrl) {
    // Create form element
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = actionUrl;
    form.style.display = 'none'; // Hide the form from view

    // Create and add CSRF token input
    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = '_token';
    csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    // Append CSRF token to form
    form.appendChild(csrfToken);

    // Append form to document body
    document.body.appendChild(form);

    // Submit the form
    form.submit();

    // Clean up: remove the form from DOM after submission
    // Note: This cleanup may not execute if the page redirects immediately
    setTimeout(() => {
        if (form.parentNode) {
            form.parentNode.removeChild(form);
        }
    }, 100);
}
</script>
@endpush
